package com.example.demo.integration;

import com.example.demo.CreateTodoRequest;
import com.example.demo.Todo;
import com.example.demo.TodoRepository;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TodoControllerTest extends AbstractIntegrationTest{

  @Autowired
  private TodoRepository todoRepository;

  @Test
  void shouldCreateTodo() {
    CreateTodoRequest req = new CreateTodoRequest("Acheter du pain");

    var res = rest.postForEntity("/todos", entityWithAuth(req), Todo.class);

    assertThat(res.getStatusCode().value()).isEqualTo(201);
    assertThat(res.getBody().getTask()).isEqualTo("Acheter du pain");
    assertThat(res.getBody().getId()).isNotNull();
  }

  @Test
  void shouldListTodos() {
    todoRepository.saveAll(List.of(
        new Todo("First task"),
        new Todo("Second task"),
        new Todo("Third task")
    ));

    var res = rest.exchange("/todos", HttpMethod.GET, entityWithAuth(), new ParameterizedTypeReference<PageResponse<Todo>>() {});

    PageResponse<Todo> page = res.getBody();

    assertNotNull(page);
    assertEquals(3, page.getContent().size());
    assertEquals(3, page.getTotalElements());
    assertEquals(1, page.getTotalPages());
  }

  static class PageResponse<T> {
    private List<T> content;
    private int number;
    private int size;
    private int totalPages;
    private long totalElements;
    private boolean last;
    private boolean first;
    private boolean empty;

    public List<T> getContent() {
      return content;
    }

    public long getTotalElements() {
      return totalElements;
    }

    public int getTotalPages() {
      return totalPages;
    }
  }
}


